package sirobilt.meghasanjivini.patientregistration.model

import jakarta.persistence.*
import java.time.OffsetDateTime

/**
 * Entity for storing duplicate detection configuration
 */
@Entity
@Table(name = "duplicate_detection_config")
class DuplicateDetectionConfigEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "config_id")
    var configId: Long = 0,

    @Column(name = "config_key", unique = true, nullable = false, length = 100)
    var configKey: String = "",

    @Column(name = "config_value", nullable = false, length = 500)
    var configValue: String = "",

    @Column(name = "description")
    var description: String? = null,

    @Column(name = "is_active")
    var isActive: Boolean = true,

    @Column(name = "created_at")
    var createdAt: OffsetDateTime = OffsetDateTime.now(),

    @Column(name = "updated_at")
    var updatedAt: OffsetDateTime = OffsetDateTime.now()
)

/**
 * Entity for storing duplicate detection audit logs
 */
@Entity
@Table(name = "duplicate_detection_logs")
class DuplicateDetectionLogEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "log_id")
    var logId: Long = 0,

    @Column(name = "patient_id")
    var patientId: String? = null,

    @Column(name = "detection_type", nullable = false, length = 20)
    var detectionType: String = "", // 'REGISTRATION', 'BATCH', 'MANUAL'

    @Column(name = "match_score", nullable = false)
    var matchScore: Int = 0,

    @Column(name = "confidence_level", nullable = false, length = 10)
    var confidenceLevel: String = "", // 'HIGH', 'MEDIUM', 'LOW'

    @Column(name = "potential_duplicates", columnDefinition = "jsonb")
    var potentialDuplicates: String? = null, // JSON array of potential duplicate patient IDs with scores

    @Column(name = "matching_criteria", columnDefinition = "jsonb")
    var matchingCriteria: String? = null, // JSON details of what criteria matched

    @Column(name = "action_taken", length = 20)
    var actionTaken: String? = null, // 'BLOCKED', 'FLAGGED', 'APPROVED', 'MERGED'

    @Column(name = "reviewed_by")
    var reviewedBy: String? = null,

    @Column(name = "review_notes")
    var reviewNotes: String? = null,

    @Column(name = "detection_time")
    var detectionTime: OffsetDateTime = OffsetDateTime.now(),

    @Column(name = "review_time")
    var reviewTime: OffsetDateTime? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id", insertable = false, updatable = false)
    var patient: Patient? = null
)

/**
 * Entity for tracking confirmed duplicate patient relationships
 */
@Entity
@Table(name = "duplicate_patient_relationships")
class DuplicatePatientRelationshipEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "relationship_id")
    var relationshipId: Long = 0,

    @Column(name = "primary_patient_id", nullable = false)
    var primaryPatientId: String = "",

    @Column(name = "duplicate_patient_id", nullable = false)
    var duplicatePatientId: String = "",

    @Column(name = "relationship_type", nullable = false, length = 20)
    var relationshipType: String = "", // 'CONFIRMED_DUPLICATE', 'FALSE_POSITIVE'

    @Column(name = "confidence_score", nullable = false)
    var confidenceScore: Int = 0,

    @Column(name = "identified_by")
    var identifiedBy: String? = null,

    @Column(name = "identified_at")
    var identifiedAt: OffsetDateTime = OffsetDateTime.now(),

    @Column(name = "notes")
    var notes: String? = null,

    @Column(name = "is_active")
    var isActive: Boolean = true,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "primary_patient_id", insertable = false, updatable = false)
    var primaryPatient: Patient? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "duplicate_patient_id", insertable = false, updatable = false)
    var duplicatePatient: Patient? = null
)
